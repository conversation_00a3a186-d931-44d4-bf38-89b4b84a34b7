import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { Order, OrderDocument, OrderStatus } from './schemas/order.schema';
import { CreateOrderDto } from './dto/create-order.dto';
import { BankCardsService } from '../bank-cards/bank-cards.service';
import { CurrenciesService } from '../currencies/currencies.service';

describe('OrdersService', () => {
  let service: OrdersService;
  let model: Model<OrderDocument>;
  let bankCardsService: BankCardsService;
  let currenciesService: CurrenciesService;

  const mockOrder = {
    _id: new Types.ObjectId('507f1f77bcf86cd799439012'),
    userId: new Types.ObjectId('507f1f77bcf86cd799439011'),
    cardId: new Types.ObjectId('507f1f77bcf86cd799439013'),
    fromCurrency: 'USD',
    toCurrency: 'IRR',
    fromAmount: 100,
    toAmount: 5000000,
    exchangeRate: 50000,
    status: OrderStatus.PENDING,
    save: jest.fn().mockResolvedValue(this),
  };

  const mockOrderModel = {
    new: jest.fn().mockImplementation(() => ({
      ...mockOrder,
      save: jest.fn().mockResolvedValue(mockOrder),
    })),
    constructor: jest.fn().mockImplementation(() => ({
      ...mockOrder,
      save: jest.fn().mockResolvedValue(mockOrder),
    })),
    find: jest.fn(),
    findOne: jest.fn(),
    findOneAndUpdate: jest.fn(),
    deleteOne: jest.fn(),
    create: jest.fn(),
    exec: jest.fn(),
  };

  // Make the mock function callable as a constructor
  Object.setPrototypeOf(mockOrderModel, Function.prototype);

  const mockBankCardsService = {
    findOne: jest.fn(),
  };

  const mockCurrenciesService = {
    findByName: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrdersService,
        {
          provide: getModelToken(Order.name),
          useValue: mockOrderModel,
        },
        {
          provide: BankCardsService,
          useValue: mockBankCardsService,
        },
        {
          provide: CurrenciesService,
          useValue: mockCurrenciesService,
        },
      ],
    }).compile();

    service = module.get<OrdersService>(OrdersService);
    model = module.get<Model<OrderDocument>>(getModelToken(Order.name));
    bankCardsService = module.get<BankCardsService>(BankCardsService);
    currenciesService = module.get<CurrenciesService>(CurrenciesService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    const createOrderDto: CreateOrderDto = {
      cardId: '507f1f77bcf86cd799439013',
      fromCurrency: 'USD',
      toCurrency: 'IRR',
      fromAmount: 100,
      toAmount: 5000000,
      exchangeRate: 50000,
    };

    it('should create an order successfully', async () => {
      const userId = '507f1f77bcf86cd799439011';

      mockBankCardsService.findOne.mockResolvedValue({ _id: createOrderDto.cardId });
      mockCurrenciesService.findByName.mockResolvedValue({ name: 'USD' });

      const result = await service.create(userId, createOrderDto);

      expect(bankCardsService.findOne).toHaveBeenCalledWith(createOrderDto.cardId, userId);
      expect(currenciesService.findByName).toHaveBeenCalledWith(createOrderDto.fromCurrency);
      expect(currenciesService.findByName).toHaveBeenCalledWith(createOrderDto.toCurrency);
      expect(mockOrderModel).toHaveBeenCalled();
    });

    it('should throw ForbiddenException when card does not belong to user', async () => {
      const userId = '507f1f77bcf86cd799439011';
      
      mockBankCardsService.findOne.mockRejectedValue(new Error('Card not found'));

      await expect(service.create(userId, createOrderDto)).rejects.toThrow(ForbiddenException);
    });

    it('should throw BadRequestException when from currency does not exist', async () => {
      const userId = '507f1f77bcf86cd799439011';
      
      mockBankCardsService.findOne.mockResolvedValue({ _id: createOrderDto.cardId });
      mockCurrenciesService.findByName.mockRejectedValueOnce(new Error('Currency not found'));

      await expect(service.create(userId, createOrderDto)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException when to currency does not exist', async () => {
      const userId = '507f1f77bcf86cd799439011';
      
      mockBankCardsService.findOne.mockResolvedValue({ _id: createOrderDto.cardId });
      mockCurrenciesService.findByName.mockResolvedValueOnce({ name: 'USD' });
      mockCurrenciesService.findByName.mockRejectedValueOnce(new Error('Currency not found'));

      await expect(service.create(userId, createOrderDto)).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException when exchange rate calculation is incorrect', async () => {
      const userId = '507f1f77bcf86cd799439011';
      const incorrectOrderDto = {
        ...createOrderDto,
        toAmount: 1000000, // Incorrect amount
      };
      
      mockBankCardsService.findOne.mockResolvedValue({ _id: createOrderDto.cardId });
      mockCurrenciesService.findByName.mockResolvedValue({ name: 'USD' });

      await expect(service.create(userId, incorrectOrderDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findOne', () => {
    it('should return an order when found', async () => {
      const orderId = '507f1f77bcf86cd799439012';
      const userId = '507f1f77bcf86cd799439011';

      mockOrderModel.findOne.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          populate: jest.fn().mockReturnValue({
            exec: jest.fn().mockResolvedValue(mockOrder),
          }),
        }),
      });

      const result = await service.findOne(orderId, userId);

      expect(result).toEqual(mockOrder);
    });

    it('should throw NotFoundException when order not found', async () => {
      const orderId = '507f1f77bcf86cd799439012';
      const userId = '507f1f77bcf86cd799439011';

      mockOrderModel.findOne.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          populate: jest.fn().mockReturnValue({
            exec: jest.fn().mockResolvedValue(null),
          }),
        }),
      });

      await expect(service.findOne(orderId, userId)).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for invalid order ID', async () => {
      const invalidOrderId = 'invalid-id';
      const userId = '507f1f77bcf86cd799439011';

      await expect(service.findOne(invalidOrderId, userId)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findAllByUser', () => {
    it('should return all orders for a user', async () => {
      const userId = '507f1f77bcf86cd799439011';
      const orders = [mockOrder];

      mockOrderModel.find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            exec: jest.fn().mockResolvedValue(orders),
          }),
        }),
      });

      const result = await service.findAllByUser(userId);

      expect(result).toEqual(orders);
      expect(mockOrderModel.find).toHaveBeenCalledWith({ userId: new Types.ObjectId(userId) });
    });
  });
});
