import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Order, OrderDocument } from './schemas/order.schema';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { BankCardsService } from '../bank-cards/bank-cards.service';
import { CurrenciesService } from '../currencies/currencies.service';

@Injectable()
export class OrdersService {
  constructor(
    @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
    private bankCardsService: BankCardsService,
    private currenciesService: CurrenciesService,
  ) {}

  async create(userId: string, createOrderDto: CreateOrderDto): Promise<Order> {
    // Validate that the card belongs to the user
    try {
      await this.bankCardsService.findOne(createOrderDto.cardId, userId);
    } catch (error) {
      throw new ForbiddenException('The specified card does not belong to you or does not exist');
    }

    // Validate that currencies exist
    try {
      await this.currenciesService.findByName(createOrderDto.fromCurrency);
    } catch (error) {
      throw new BadRequestException(`From currency '${createOrderDto.fromCurrency}' does not exist`);
    }

    try {
      await this.currenciesService.findByName(createOrderDto.toCurrency);
    } catch (error) {
      throw new BadRequestException(`To currency '${createOrderDto.toCurrency}' does not exist`);
    }

    // Validate exchange rate calculation
    const calculatedToAmount = createOrderDto.fromAmount * createOrderDto.exchangeRate;
    const tolerance = 0.01; // Allow 1% tolerance for rounding
    const difference = Math.abs(calculatedToAmount - createOrderDto.toAmount);
    const percentageDifference = (difference / createOrderDto.toAmount) * 100;

    if (percentageDifference > tolerance) {
      throw new BadRequestException(
        `Exchange rate calculation mismatch. Expected: ${calculatedToAmount.toFixed(4)}, Received: ${createOrderDto.toAmount}`
      );
    }

    const order = new this.orderModel({
      ...createOrderDto,
      userId: new Types.ObjectId(userId),
      cardId: new Types.ObjectId(createOrderDto.cardId),
    });

    return order.save();
  }

  async findAll(): Promise<Order[]> {
    return this.orderModel.find().populate('userId', 'mobileNumber').populate('cardId', 'bankName accountHolderName').exec();
  }

  async findAllByUser(userId: string): Promise<Order[]> {
    return this.orderModel
      .find({ userId: new Types.ObjectId(userId) })
      .populate('cardId', 'bankName accountHolderName')
      .sort({ createdAt: -1 })
      .exec();
  }

  async findOne(id: string, userId?: string): Promise<Order> {
    if (!Types.ObjectId.isValid(id)) {
      throw new BadRequestException('Invalid order ID format');
    }

    const query: any = { _id: new Types.ObjectId(id) };
    if (userId) {
      query.userId = new Types.ObjectId(userId);
    }

    const order = await this.orderModel
      .findOne(query)
      .populate('userId', 'mobileNumber')
      .populate('cardId', 'bankName accountHolderName')
      .exec();

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    return order;
  }

  async update(id: string, userId: string, updateOrderDto: UpdateOrderDto): Promise<Order> {
    const order = await this.findOne(id, userId);

    // If cardId is being updated, validate it belongs to the user
    if (updateOrderDto.cardId && updateOrderDto.cardId !== order.cardId.toString()) {
      try {
        await this.bankCardsService.findOne(updateOrderDto.cardId, userId);
      } catch (error) {
        throw new ForbiddenException('The specified card does not belong to you or does not exist');
      }
    }

    // Validate currencies if they are being updated
    if (updateOrderDto.fromCurrency) {
      try {
        await this.currenciesService.findByName(updateOrderDto.fromCurrency);
      } catch (error) {
        throw new BadRequestException(`From currency '${updateOrderDto.fromCurrency}' does not exist`);
      }
    }

    if (updateOrderDto.toCurrency) {
      try {
        await this.currenciesService.findByName(updateOrderDto.toCurrency);
      } catch (error) {
        throw new BadRequestException(`To currency '${updateOrderDto.toCurrency}' does not exist`);
      }
    }

    const updatedOrder = await this.orderModel.findOneAndUpdate(
      { _id: new Types.ObjectId(id), userId: new Types.ObjectId(userId) },
      updateOrderDto,
      { new: true }
    ).populate('userId', 'mobileNumber').populate('cardId', 'bankName accountHolderName').exec();

    if (!updatedOrder) {
      throw new NotFoundException('Order not found');
    }

    return updatedOrder;
  }

  async remove(id: string, userId: string): Promise<void> {
    const order = await this.findOne(id, userId);
    await this.orderModel.deleteOne({ _id: new Types.ObjectId(id) }).exec();
  }

  async getOrdersByStatus(status: string, userId?: string): Promise<Order[]> {
    const query: any = { status };
    if (userId) {
      query.userId = new Types.ObjectId(userId);
    }

    return this.orderModel
      .find(query)
      .populate('userId', 'mobileNumber')
      .populate('cardId', 'bankName accountHolderName')
      .sort({ createdAt: -1 })
      .exec();
  }
}
