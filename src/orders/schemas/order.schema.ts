import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type OrderDocument = Order & Document;

export enum OrderStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
}

@Schema({
  timestamps: true,
})
export class Order {
  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: 'User',
  })
  userId: Types.ObjectId;

  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: 'BankCard',
  })
  cardId: Types.ObjectId;

  @Prop({
    required: true,
    minlength: 1,
    maxlength: 10,
  })
  fromCurrency: string;

  @Prop({
    required: true,
    minlength: 1,
    maxlength: 10,
  })
  toCurrency: string;

  @Prop({
    required: true,
    min: 0.01,
  })
  fromAmount: number;

  @Prop({
    required: true,
    min: 0.01,
  })
  toAmount: number;

  @Prop({
    required: true,
    min: 0.0001,
  })
  exchangeRate: number;

  @Prop({
    maxlength: 100,
  })
  depositId?: string;

  @Prop({
    type: String,
    enum: OrderStatus,
    default: OrderStatus.PENDING,
  })
  status: OrderStatus;
}

export const OrderSchema = SchemaFactory.createForClass(Order);

// Indexes for efficient queries
OrderSchema.index({ userId: 1 });
OrderSchema.index({ status: 1 });
OrderSchema.index({ createdAt: -1 });
OrderSchema.index({ userId: 1, status: 1 });
