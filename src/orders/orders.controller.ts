import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  BadRequestException,
  Query,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@Controller('orders')
@UseGuards(JwtAuthGuard)
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  /**
   * Create a new order for the authenticated user
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Request() req: any, @Body() createOrderDto: CreateOrderDto) {
    try {
      const userId = req.user.sub;
      const order = await this.ordersService.create(userId, createOrderDto);
      
      return {
        success: true,
        message: 'Order created successfully',
        data: order,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Get all orders for the authenticated user
   */
  @Get()
  async findAll(@Request() req: any, @Query('status') status?: string) {
    try {
      const userId = req.user.sub;
      let orders;
      
      if (status) {
        orders = await this.ordersService.getOrdersByStatus(status, userId);
      } else {
        orders = await this.ordersService.findAllByUser(userId);
      }
      
      return {
        success: true,
        message: 'Orders retrieved successfully',
        data: orders,
        count: orders.length,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Get a specific order by ID for the authenticated user
   */
  @Get(':id')
  async findOne(@Param('id') id: string, @Request() req: any) {
    try {
      const userId = req.user.sub;
      const order = await this.ordersService.findOne(id, userId);
      
      return {
        success: true,
        message: 'Order retrieved successfully',
        data: order,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Update an order for the authenticated user
   */
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Request() req: any,
    @Body() updateOrderDto: UpdateOrderDto,
  ) {
    try {
      const userId = req.user.sub;
      const order = await this.ordersService.update(id, userId, updateOrderDto);
      
      return {
        success: true,
        message: 'Order updated successfully',
        data: order,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Delete an order for the authenticated user
   */
  // @Delete(':id')
  // @HttpCode(HttpStatus.NO_CONTENT)
  // async remove(@Param('id') id: string, @Request() req: any) {
  //   try {
  //     const userId = req.user.sub;
  //     await this.ordersService.remove(id, userId);
      
  //     return {
  //       success: true,
  //       message: 'Order deleted successfully',
  //     };
  //   } catch (error) {
  //     throw new BadRequestException(error.message);
  //   }
  // }
}
