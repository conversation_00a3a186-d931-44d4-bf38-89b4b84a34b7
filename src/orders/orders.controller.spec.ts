import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { OrdersController } from './orders.controller';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderStatus } from './schemas/order.schema';

describe('OrdersController', () => {
  let controller: OrdersController;
  let service: OrdersService;

  const mockOrdersService = {
    create: jest.fn(),
    findAllByUser: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    getOrdersByStatus: jest.fn(),
  };

  const mockRequest = {
    user: {
      sub: '507f1f77bcf86cd799439011',
    },
  };

  const mockOrder = {
    _id: '507f1f77bcf86cd799439012',
    userId: '507f1f77bcf86cd799439011',
    cardId: '507f1f77bcf86cd799439013',
    fromCurrency: 'USD',
    toCurrency: 'IRR',
    fromAmount: 100,
    toAmount: 5000000,
    exchangeRate: 50000,
    status: OrderStatus.PENDING,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrdersController],
      providers: [
        {
          provide: OrdersService,
          useValue: mockOrdersService,
        },
      ],
    })
    .overrideGuard(require('../auth/jwt-auth.guard').JwtAuthGuard)
    .useValue({ canActivate: () => true })
    .compile();

    controller = module.get<OrdersController>(OrdersController);
    service = module.get<OrdersService>(OrdersService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new order successfully', async () => {
      const createOrderDto: CreateOrderDto = {
        cardId: '507f1f77bcf86cd799439013',
        fromCurrency: 'USD',
        toCurrency: 'IRR',
        fromAmount: 100,
        toAmount: 5000000,
        exchangeRate: 50000,
      };

      mockOrdersService.create.mockResolvedValue(mockOrder);

      const result = await controller.create(mockRequest, createOrderDto);

      expect(service.create).toHaveBeenCalledWith(mockRequest.user.sub, createOrderDto);
      expect(result).toEqual({
        success: true,
        message: 'Order created successfully',
        data: mockOrder,
      });
    });

    it('should throw BadRequestException when service throws error', async () => {
      const createOrderDto: CreateOrderDto = {
        cardId: '507f1f77bcf86cd799439013',
        fromCurrency: 'USD',
        toCurrency: 'IRR',
        fromAmount: 100,
        toAmount: 5000000,
        exchangeRate: 50000,
      };

      mockOrdersService.create.mockRejectedValue(new Error('Card not found'));

      await expect(controller.create(mockRequest, createOrderDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findAll', () => {
    it('should return all orders for user', async () => {
      const orders = [mockOrder];
      mockOrdersService.findAllByUser.mockResolvedValue(orders);

      const result = await controller.findAll(mockRequest);

      expect(service.findAllByUser).toHaveBeenCalledWith(mockRequest.user.sub);
      expect(result).toEqual({
        success: true,
        message: 'Orders retrieved successfully',
        data: orders,
        count: orders.length,
      });
    });

    it('should return orders filtered by status', async () => {
      const orders = [mockOrder];
      mockOrdersService.getOrdersByStatus.mockResolvedValue(orders);

      const result = await controller.findAll(mockRequest, 'pending');

      expect(service.getOrdersByStatus).toHaveBeenCalledWith('pending', mockRequest.user.sub);
      expect(result).toEqual({
        success: true,
        message: 'Orders retrieved successfully',
        data: orders,
        count: orders.length,
      });
    });
  });

  describe('findOne', () => {
    it('should return a specific order', async () => {
      mockOrdersService.findOne.mockResolvedValue(mockOrder);

      const result = await controller.findOne('507f1f77bcf86cd799439012', mockRequest);

      expect(service.findOne).toHaveBeenCalledWith('507f1f77bcf86cd799439012', mockRequest.user.sub);
      expect(result).toEqual({
        success: true,
        message: 'Order retrieved successfully',
        data: mockOrder,
      });
    });
  });

  describe('update', () => {
    it('should update an order successfully', async () => {
      const updateOrderDto: UpdateOrderDto = {
        status: OrderStatus.PROCESSING,
      };

      const updatedOrder = { ...mockOrder, status: OrderStatus.PROCESSING };
      mockOrdersService.update.mockResolvedValue(updatedOrder);

      const result = await controller.update('507f1f77bcf86cd799439012', mockRequest, updateOrderDto);

      expect(service.update).toHaveBeenCalledWith('507f1f77bcf86cd799439012', mockRequest.user.sub, updateOrderDto);
      expect(result).toEqual({
        success: true,
        message: 'Order updated successfully',
        data: updatedOrder,
      });
    });
  });

  describe('remove', () => {
    it('should delete an order successfully', async () => {
      mockOrdersService.remove.mockResolvedValue(undefined);

      const result = await controller.remove('507f1f77bcf86cd799439012', mockRequest);

      expect(service.remove).toHaveBeenCalledWith('507f1f77bcf86cd799439012', mockRequest.user.sub);
      expect(result).toEqual({
        success: true,
        message: 'Order deleted successfully',
      });
    });
  });
});
