# Orders Module

This module provides CRUD operations for currency exchange orders with JWT authentication and comprehensive validation.

## Features

- ✅ JWT Authentication required for all endpoints
- ✅ User-specific order management (users can only access their own orders)
- ✅ Bank card ownership validation
- ✅ Currency existence validation
- ✅ Exchange rate calculation validation
- ✅ Order status management
- ✅ Comprehensive error handling and validation

## API Endpoints

### POST /orders
Creates a new currency exchange order for the authenticated user.

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Request Body:**
```json
{
  "cardId": "507f1f77bcf86cd799439013",
  "fromCurrency": "USD",
  "toCurrency": "IRR",
  "fromAmount": 100,
  "toAmount": 5000000,
  "exchangeRate": 50000,
  "depositId": "DEP123456789"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Order created successfully",
  "data": {
    "_id": "507f1f77bcf86cd799439012",
    "userId": "507f1f77bcf86cd799439011",
    "cardId": "507f1f77bcf86cd799439013",
    "fromCurrency": "USD",
    "toCurrency": "IRR",
    "fromAmount": 100,
    "toAmount": 5000000,
    "exchangeRate": 50000,
    "depositId": "DEP123456789",
    "status": "pending",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### GET /orders
Returns all orders for the authenticated user.

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Query Parameters:**
- `status` (optional): Filter orders by status (pending, processing, completed, cancelled, failed)

**Response:**
```json
{
  "success": true,
  "message": "Orders retrieved successfully",
  "data": [
    {
      "_id": "507f1f77bcf86cd799439012",
      "userId": "507f1f77bcf86cd799439011",
      "cardId": {
        "_id": "507f1f77bcf86cd799439013",
        "bankName": "Bank Melli",
        "accountHolderName": "John Doe"
      },
      "fromCurrency": "USD",
      "toCurrency": "IRR",
      "fromAmount": 100,
      "toAmount": 5000000,
      "exchangeRate": 50000,
      "status": "pending",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "count": 1
}
```

### GET /orders/:id
Returns a specific order by ID for the authenticated user.

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Response:**
```json
{
  "success": true,
  "message": "Order retrieved successfully",
  "data": {
    "_id": "507f1f77bcf86cd799439012",
    "userId": {
      "_id": "507f1f77bcf86cd799439011",
      "mobileNumber": "***********"
    },
    "cardId": {
      "_id": "507f1f77bcf86cd799439013",
      "bankName": "Bank Melli",
      "accountHolderName": "John Doe"
    },
    "fromCurrency": "USD",
    "toCurrency": "IRR",
    "fromAmount": 100,
    "toAmount": 5000000,
    "exchangeRate": 50000,
    "status": "pending",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### PATCH /orders/:id
Updates an existing order for the authenticated user.

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Request Body:**
```json
{
  "status": "processing"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Order updated successfully",
  "data": {
    "_id": "507f1f77bcf86cd799439012",
    "status": "processing",
    "updatedAt": "2023-01-01T01:00:00.000Z"
  }
}
```

### DELETE /orders/:id
Deletes an order for the authenticated user.

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Response:**
```json
{
  "success": true,
  "message": "Order deleted successfully"
}
```

## Validation Rules

- **Card ID**: Must be a valid MongoDB ObjectId and belong to the authenticated user
- **From Currency**: Must be a non-empty string (max 10 characters) and exist in the currencies collection
- **To Currency**: Must be a non-empty string (max 10 characters) and exist in the currencies collection
- **From Amount**: Must be a positive number (minimum 0.01)
- **To Amount**: Must be a positive number (minimum 0.01)
- **Exchange Rate**: Must be a positive number (minimum 0.0001)
- **Deposit ID**: Optional, maximum 100 characters
- **Status**: Must be one of: pending, processing, completed, cancelled, failed

## Business Logic Validation

- **Exchange Rate Calculation**: The system validates that `fromAmount * exchangeRate ≈ toAmount` with 1% tolerance
- **Card Ownership**: Users can only create orders with their own bank cards
- **Currency Existence**: Both from and to currencies must exist in the currencies collection

## Order Status Flow

1. **pending**: Initial status when order is created
2. **processing**: Order is being processed
3. **completed**: Order has been successfully completed
4. **cancelled**: Order was cancelled by user or system
5. **failed**: Order processing failed

## Security Features

- JWT authentication required for all endpoints
- User-specific access control (users can only access their own orders)
- Bank card ownership validation
- Currency existence validation
- Exchange rate calculation validation
- Comprehensive error handling with appropriate HTTP status codes

## Database Schema

### Order Schema
```typescript
{
  userId: ObjectId;           // Reference to User, required
  cardId: ObjectId;          // Reference to BankCard, required
  fromCurrency: string;      // Source currency, required, max 10 chars
  toCurrency: string;        // Target currency, required, max 10 chars
  fromAmount: number;        // Source amount, required, min 0.01
  toAmount: number;          // Target amount, required, min 0.01
  exchangeRate: number;      // Exchange rate, required, min 0.0001
  depositId?: string;        // Optional deposit ID, max 100 chars
  status: OrderStatus;       // Order status, default: pending
  createdAt: Date;           // Auto-generated
  updatedAt: Date;           // Auto-generated
}
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- **400 Bad Request**: Invalid input data, validation errors
- **401 Unauthorized**: Missing or invalid JWT token
- **403 Forbidden**: User trying to access resources they don't own
- **404 Not Found**: Order not found
- **500 Internal Server Error**: Server-side errors
