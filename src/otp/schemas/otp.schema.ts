import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type OtpDocument = Otp & Document;

@Schema({
  timestamps: true,
})
export class Otp {
  @Prop({ 
    required: true,
    match: /^(\+98|0)?9\d{9}$/ // Iranian mobile number format
  })
  mobileNumber: string;

  @Prop({ required: true, length: 6 })
  code: string;

  @Prop({ 
    required: true,
    default: () => new Date(Date.now() + 5 * 60 * 1000) // 5 minutes from now
  })
  expiresAt: Date;

  @Prop({ default: false })
  isUsed: boolean;

  @Prop({ default: 0 })
  attempts: number;
}

export const OtpSchema = SchemaFactory.createForClass(Otp);
