import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { OtpService } from './otp.service';
import { Otp, OtpDocument } from './schemas/otp.schema';

describe('OtpService', () => {
  let service: OtpService;
  let otpModel: Model<OtpDocument>;

  const mockOtp = {
    _id: 'mockId',
    mobileNumber: '09123456789',
    code: '123456',
    expiresAt: new Date(Date.now() + 5 * 60 * 1000),
    isUsed: false,
    attempts: 0,
    save: jest.fn().mockResolvedValue(this),
  };

  const mockOtpModel = {
    findOne: jest.fn(),
    updateMany: jest.fn(),
    deleteMany: jest.fn(),
    constructor: jest.fn().mockImplementation(() => ({
      ...mockOtp,
      save: jest.fn().mockResolvedValue(mockOtp),
    })),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OtpService,
        {
          provide: getModelToken(Otp.name),
          useValue: mockOtpModel,
        },
      ],
    }).compile();

    service = module.get<OtpService>(OtpService);
    otpModel = module.get<Model<OtpDocument>>(getModelToken(Otp.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createOtp', () => {
    it('should create a new OTP', async () => {
      mockOtpModel.updateMany.mockResolvedValue({ modifiedCount: 0 });
      (otpModel as any).mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(mockOtp),
      }));

      const result = await service.createOtp('09123456789');

      expect(result.code).toMatch(/^\d{6}$/);
      expect(result.expiresAt).toBeInstanceOf(Date);
      expect(mockOtpModel.updateMany).toHaveBeenCalled();
    });
  });

  describe('verifyOtp', () => {
    it('should verify valid OTP', async () => {
      const validOtp = {
        ...mockOtp,
        save: jest.fn().mockResolvedValue(mockOtp),
      };

      mockOtpModel.findOne.mockResolvedValue(validOtp);

      const result = await service.verifyOtp('09123456789', '123456');

      expect(result).toBe(true);
      expect(validOtp.isUsed).toBe(true);
      expect(validOtp.save).toHaveBeenCalled();
    });

    it('should reject invalid OTP', async () => {
      mockOtpModel.findOne.mockResolvedValue(null);
      mockOtpModel.updateMany.mockResolvedValue({ modifiedCount: 1 });

      const result = await service.verifyOtp('09123456789', '123456');

      expect(result).toBe(false);
      expect(mockOtpModel.updateMany).toHaveBeenCalled();
    });
  });

  describe('cleanupExpiredOtps', () => {
    it('should delete expired OTPs', async () => {
      mockOtpModel.deleteMany.mockResolvedValue({ deletedCount: 5 });

      await service.cleanupExpiredOtps();

      expect(mockOtpModel.deleteMany).toHaveBeenCalledWith({
        expiresAt: { $lt: expect.any(Date) }
      });
    });
  });
});
