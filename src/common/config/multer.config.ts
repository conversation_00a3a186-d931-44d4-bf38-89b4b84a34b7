import { diskStorage } from 'multer';
import { extname, join } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { BadRequestException } from '@nestjs/common';

// Ensure uploads directory exists
const uploadPath = join(process.cwd(), 'uploads', 'user-documents');
if (!existsSync(uploadPath)) {
  mkdirSync(uploadPath, { recursive: true });
}

export const multerConfig = {
  storage: diskStorage({
    destination: (req, file, cb) => {
      cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
      // Generate unique filename: userId_fieldname_timestamp.ext
      const userId = (req as any).user?.sub || 'unknown';
      const timestamp = Date.now();
      const ext = extname(file.originalname);
      const filename = `${userId}_${file.fieldname}_${timestamp}${ext}`;
      cb(null, filename);
    },
  }),
  fileFilter: (req, file, cb) => {
    // Only allow image files
    if (!file.mimetype.match(/\/(jpg|jpeg|png|gif)$/)) {
      return cb(
        new BadRequestException('Only image files (jpg, jpeg, png, gif) are allowed'),
        false,
      );
    }
    cb(null, true);
  },
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
};
