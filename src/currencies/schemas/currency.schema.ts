import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type CurrencyDocument = Currency & Document;

@Schema({
  timestamps: true,
})
export class Currency {
  @Prop({ required: true, unique: true })
  name: string;

  @Prop({ required: true, unique: true })
  fa: string;

  @Prop({ required: true })
  sellPrice: number;

  @Prop({ required: true })
  buyPrice: number;
}

export const CurrencySchema = SchemaFactory.createForClass(Currency);
