import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Currency, CurrencyDocument } from './schemas/currency.schema';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';

@Injectable()
export class CurrenciesService {
  constructor(
    @InjectModel(Currency.name) private currencyModel: Model<CurrencyDocument>,
  ) {}

  async create(createCurrencyDto: CreateCurrencyDto): Promise<Currency> {
    const createdCurrency = new this.currencyModel(createCurrencyDto);
    return createdCurrency.save();
  }

  async findAll(): Promise<Currency[]> {
    return this.currencyModel.find().exec();
  }

  async findOne(id: string): Promise<Currency> {
    const currency = await this.currencyModel.findById(id).exec();
    if (!currency) {
      throw new NotFoundException(`Currency with ID ${id} not found`);
    }
    return currency;
  }

  async update(id: string, updateCurrencyDto: UpdateCurrencyDto): Promise<Currency> {
    const updatedCurrency = await this.currencyModel
      .findByIdAndUpdate(id, updateCurrencyDto, { new: true })
      .exec();
    
    if (!updatedCurrency) {
      throw new NotFoundException(`Currency with ID ${id} not found`);
    }
    
    return updatedCurrency;
  }

  async remove(id: string): Promise<void> {
    const result = await this.currencyModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException(`Currency with ID ${id} not found`);
    }
  }

  async findByName(name: string): Promise<Currency> {
    const currency = await this.currencyModel.findOne({ name }).exec();
    if (!currency) {
      throw new NotFoundException(`Currency with name ${name} not found`);
    }
    return currency;
  }
}
