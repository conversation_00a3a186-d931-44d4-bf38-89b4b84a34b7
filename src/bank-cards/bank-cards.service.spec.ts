import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common';
import { BankCardsService } from './bank-cards.service';
import { BankCard, BankCardDocument } from './schemas/bank-card.schema';
import { CreateBankCardDto } from './dto/create-bank-card.dto';
import { UpdateBankCardDto } from './dto/update-bank-card.dto';

describe('BankCardsService', () => {
  let service: BankCardsService;
  let model: Model<BankCardDocument>;

  const mockUserId = new Types.ObjectId().toString();
  const mockBankCardId = new Types.ObjectId().toString();

  const mockBankCard = {
    _id: mockBankCardId,
    userId: new Types.ObjectId(mockUserId),
    cardNumber: '****************',
    bankName: 'Test Bank',
    accountHolderName: '<PERSON> Do<PERSON>',
    iban: '**************************',
    isActive: true,
    description: 'Test card',
    createdAt: new Date(),
    updatedAt: new Date(),
    save: jest.fn().mockResolvedValue(this),
  };

  const mockBankCardModel = jest.fn().mockImplementation(() => ({
    save: jest.fn().mockResolvedValue(mockBankCard),
  }));

  Object.assign(mockBankCardModel, {
    find: jest.fn(),
    findById: jest.fn(),
    findOne: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    countDocuments: jest.fn(),
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BankCardsService,
        {
          provide: getModelToken(BankCard.name),
          useValue: mockBankCardModel,
        },
      ],
    }).compile();

    service = module.get<BankCardsService>(BankCardsService);
    model = module.get<Model<BankCardDocument>>(getModelToken(BankCard.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new bank card', async () => {
      const createBankCardDto: CreateBankCardDto = {
        cardNumber: '****************',
        bankName: 'Test Bank',
        accountHolderName: 'John Doe',
        iban: '**************************',
        description: 'Test card',
      };

      jest.spyOn(model, 'findOne').mockReturnValueOnce({
        exec: jest.fn().mockResolvedValue(null),
      } as any).mockReturnValueOnce({
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      const result = await service.create(mockUserId, createBankCardDto);

      expect(result).toBeDefined();
      expect(result.cardNumber).toBe('**** **** **** 3456');
      expect(result.bankName).toBe(createBankCardDto.bankName);
    });

    it('should throw ConflictException if card number already exists', async () => {
      const createBankCardDto: CreateBankCardDto = {
        cardNumber: '****************',
        bankName: 'Test Bank',
        accountHolderName: 'John Doe',
        iban: '**************************',
      };

      jest.spyOn(model, 'findOne').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockBankCard),
      } as any);

      await expect(service.create(mockUserId, createBankCardDto)).rejects.toThrow(ConflictException);
    });
  });

  describe('findAllByUser', () => {
    it('should return all bank cards for a user', async () => {
      const mockCards = [mockBankCard];
      jest.spyOn(model, 'find').mockReturnValue({
        sort: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockCards),
        }),
      } as any);

      const result = await service.findAllByUser(mockUserId);

      expect(result).toHaveLength(1);
      expect(result[0].cardNumber).toBe('**** **** **** 3456');
    });
  });

  describe('findOne', () => {
    it('should return a bank card if it belongs to the user', async () => {
      jest.spyOn(model, 'findById').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockBankCard),
      } as any);

      const result = await service.findOne(mockBankCardId, mockUserId);

      expect(result).toBeDefined();
      expect(result.cardNumber).toBe('**** **** **** 3456');
    });

    it('should throw NotFoundException if bank card not found', async () => {
      jest.spyOn(model, 'findById').mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.findOne(mockBankCardId, mockUserId)).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if bank card belongs to another user', async () => {
      const otherUserId = new Types.ObjectId().toString();
      jest.spyOn(model, 'findById').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockBankCard),
      } as any);

      await expect(service.findOne(mockBankCardId, otherUserId)).rejects.toThrow(ForbiddenException);
    });
  });

  describe('update', () => {
    it('should update a bank card if it belongs to the user', async () => {
      const updateBankCardDto: UpdateBankCardDto = {
        bankName: 'Updated Bank',
      };

      jest.spyOn(model, 'findById').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockBankCard),
      } as any);

      jest.spyOn(model, 'findByIdAndUpdate').mockReturnValue({
        exec: jest.fn().mockResolvedValue({
          ...mockBankCard,
          bankName: 'Updated Bank',
        }),
      } as any);

      const result = await service.update(mockBankCardId, mockUserId, updateBankCardDto);

      expect(result).toBeDefined();
      expect(result.bankName).toBe('Updated Bank');
    });

    it('should throw NotFoundException if bank card not found', async () => {
      const updateBankCardDto: UpdateBankCardDto = {
        bankName: 'Updated Bank',
      };

      jest.spyOn(model, 'findById').mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.update(mockBankCardId, mockUserId, updateBankCardDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should delete a bank card if it belongs to the user', async () => {
      jest.spyOn(model, 'findById').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockBankCard),
      } as any);

      jest.spyOn(model, 'findByIdAndDelete').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockBankCard),
      } as any);

      await expect(service.remove(mockBankCardId, mockUserId)).resolves.not.toThrow();
    });

    it('should throw NotFoundException if bank card not found', async () => {
      jest.spyOn(model, 'findById').mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.remove(mockBankCardId, mockUserId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getActiveCardsCount', () => {
    it('should return the count of active cards for a user', async () => {
      jest.spyOn(model, 'countDocuments').mockReturnValue({
        exec: jest.fn().mockResolvedValue(2),
      } as any);

      const result = await service.getActiveCardsCount(mockUserId);

      expect(result).toBe(2);
    });
  });
});
