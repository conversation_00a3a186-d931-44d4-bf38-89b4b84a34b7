import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';
import { BankCardsService } from './bank-cards.service';
import { CreateBankCardDto } from './dto/create-bank-card.dto';
import { UpdateBankCardDto } from './dto/update-bank-card.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@Controller('cards')
@UseGuards(JwtAuthGuard)
export class BankCardsController {
  constructor(private readonly bankCardsService: BankCardsService) {}

  /**
   * Create a new bank card for the authenticated user
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(@Request() req: any, @Body() createBankCardDto: CreateBankCardDto) {
    try {
      const userId = req.user.sub;
      const bankCard = await this.bankCardsService.create(userId, createBankCardDto);
      
      return {
        success: true,
        message: 'Bank card created successfully',
        data: bankCard,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Get all bank cards for the authenticated user
   */
  @Get()
  async findAll(@Request() req: any) {
    try {
      const userId = req.user.sub;
      const bankCards = await this.bankCardsService.findAllByUser(userId);
      
      return {
        success: true,
        message: 'Bank cards retrieved successfully',
        data: bankCards,
        count: bankCards.length,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Get active bank cards count for the authenticated user
   */
  @Get('active-count')
  async getActiveCount(@Request() req: any) {
    try {
      const userId = req.user.sub;
      const count = await this.bankCardsService.getActiveCardsCount(userId);
      
      return {
        success: true,
        message: 'Active bank cards count retrieved successfully',
        data: { activeCardsCount: count },
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Get a specific bank card by ID for the authenticated user
   */
  @Get(':id')
  async findOne(@Param('id') id: string, @Request() req: any) {
    try {
      const userId = req.user.sub;
      const bankCard = await this.bankCardsService.findOne(id, userId);
      
      return {
        success: true,
        message: 'Bank card retrieved successfully',
        data: bankCard,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Update a bank card for the authenticated user
   */
  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Request() req: any,
    @Body() updateBankCardDto: UpdateBankCardDto,
  ) {
    try {
      const userId = req.user.sub;
      const bankCard = await this.bankCardsService.update(id, userId, updateBankCardDto);
      
      return {
        success: true,
        message: 'Bank card updated successfully',
        data: bankCard,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Delete a bank card for the authenticated user
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string, @Request() req: any) {
    try {
      const userId = req.user.sub;
      await this.bankCardsService.remove(id, userId);
      
      return {
        success: true,
        message: 'Bank card deleted successfully',
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
