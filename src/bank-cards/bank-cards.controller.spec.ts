import { Test, TestingModule } from '@nestjs/testing';
import { BankCardsController } from './bank-cards.controller';
import { BankCardsService } from './bank-cards.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { CreateBankCardDto } from './dto/create-bank-card.dto';
import { UpdateBankCardDto } from './dto/update-bank-card.dto';

describe('BankCardsController', () => {
  let controller: BankCardsController;
  let service: BankCardsService;

  const mockUserId = 'mockUserId';
  const mockBankCardId = 'mockBankCardId';

  const mockBankCard = {
    id: mockBankCardId,
    userId: mockUserId,
    cardNumber: '**** **** **** 3456',
    bankName: 'Test Bank',
    accountHolderName: '<PERSON>',
    iban: '**************************',
    isActive: true,
    description: 'Test card',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockBankCardsService = {
    create: jest.fn(),
    findAllByUser: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    getActiveCardsCount: jest.fn(),
  };

  const mockJwtAuthGuard = {
    canActivate: jest.fn(() => true),
  };

  const mockRequest = {
    user: {
      sub: mockUserId,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BankCardsController],
      providers: [
        {
          provide: BankCardsService,
          useValue: mockBankCardsService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .compile();

    controller = module.get<BankCardsController>(BankCardsController);
    service = module.get<BankCardsService>(BankCardsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new bank card', async () => {
      const createBankCardDto: CreateBankCardDto = {
        cardNumber: '****************',
        bankName: 'Test Bank',
        accountHolderName: 'John Doe',
        iban: '**************************',
        description: 'Test card',
      };

      mockBankCardsService.create.mockResolvedValue(mockBankCard);

      const result = await controller.create(mockRequest, createBankCardDto);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Bank card created successfully');
      expect(result.data).toEqual(mockBankCard);
      expect(service.create).toHaveBeenCalledWith(mockUserId, createBankCardDto);
    });
  });

  describe('findAll', () => {
    it('should return all bank cards for the user', async () => {
      const mockBankCards = [mockBankCard];
      mockBankCardsService.findAllByUser.mockResolvedValue(mockBankCards);

      const result = await controller.findAll(mockRequest);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Bank cards retrieved successfully');
      expect(result.data).toEqual(mockBankCards);
      expect(result.count).toBe(1);
      expect(service.findAllByUser).toHaveBeenCalledWith(mockUserId);
    });
  });

  describe('getActiveCount', () => {
    it('should return active bank cards count', async () => {
      mockBankCardsService.getActiveCardsCount.mockResolvedValue(2);

      const result = await controller.getActiveCount(mockRequest);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Active bank cards count retrieved successfully');
      expect(result.data.activeCardsCount).toBe(2);
      expect(service.getActiveCardsCount).toHaveBeenCalledWith(mockUserId);
    });
  });

  describe('findOne', () => {
    it('should return a specific bank card', async () => {
      mockBankCardsService.findOne.mockResolvedValue(mockBankCard);

      const result = await controller.findOne(mockBankCardId, mockRequest);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Bank card retrieved successfully');
      expect(result.data).toEqual(mockBankCard);
      expect(service.findOne).toHaveBeenCalledWith(mockBankCardId, mockUserId);
    });
  });

  describe('update', () => {
    it('should update a bank card', async () => {
      const updateBankCardDto: UpdateBankCardDto = {
        bankName: 'Updated Bank',
      };

      const updatedBankCard = { ...mockBankCard, bankName: 'Updated Bank' };
      mockBankCardsService.update.mockResolvedValue(updatedBankCard);

      const result = await controller.update(mockBankCardId, mockRequest, updateBankCardDto);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Bank card updated successfully');
      expect(result.data).toEqual(updatedBankCard);
      expect(service.update).toHaveBeenCalledWith(mockBankCardId, mockUserId, updateBankCardDto);
    });
  });

  describe('remove', () => {
    it('should delete a bank card', async () => {
      mockBankCardsService.remove.mockResolvedValue(undefined);

      const result = await controller.remove(mockBankCardId, mockRequest);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Bank card deleted successfully');
      expect(service.remove).toHaveBeenCalledWith(mockBankCardId, mockUserId);
    });
  });
});
