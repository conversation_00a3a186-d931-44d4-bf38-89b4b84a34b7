import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type BankCardDocument = BankCard & Document;

@Schema({
  timestamps: true,
})
export class BankCard {
  @Prop({
    required: true,
    type: Types.ObjectId,
    ref: 'User',
  })
  userId: Types.ObjectId;

  @Prop({
    required: true,
    match: /^\d{16}$/, // 16-digit card number
  })
  cardNumber: string;

  @Prop({
    required: true,
    minlength: 2,
    maxlength: 50,
  })
  bankName: string;

  @Prop({
    required: true,
    minlength: 2,
    maxlength: 100,
  })
  accountHolderName: string;

  @Prop({
    required: true,
    match: /^IR\d{24}$/, // Iranian IBAN format
  })
  iban: string;

  @Prop({
    default: true,
  })
  isActive: boolean;

  @Prop({
    maxlength: 200,
  })
  description?: string;
}

export const BankCardSchema = SchemaFactory.createForClass(BankCard);

// Index for efficient user-specific queries
BankCardSchema.index({ userId: 1 });
