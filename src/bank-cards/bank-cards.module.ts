import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BankCardsService } from './bank-cards.service';
import { BankCardsController } from './bank-cards.controller';
import { BankCard, BankCardSchema } from './schemas/bank-card.schema';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: BankCard.name, schema: BankCardSchema }]),
    AuthModule,
  ],
  controllers: [BankCardsController],
  providers: [BankCardsService],
  exports: [BankCardsService],
})
export class BankCardsModule {}
