import { Prop } from '@nestjs/mongoose';
import { IsString, IsNotEmpty, Matches, Length, IsOptional } from 'class-validator';

export class CreateBankCardDto {
  @IsString()
  @IsNotEmpty()
  @Matches(/^[0-9]{16}$/, {
    message: 'شماره کارت باید دقیقاً ۱۶ رقم باشد'
  })
  cardNumber: string;

  @IsString()
  @IsNotEmpty()
  @Length(2, 50, { message: 'نام بانک باید بین ۲ تا ۵۰ کاراکتر باشد' })
  bankName: string;

  @IsString()
  @IsNotEmpty()
  @Length(2, 100, { message: 'نام دارنده حساب باید بین ۲ تا ۱۰۰ کاراکتر باشد' })
  accountHolderName: string;

  @Prop({ unique: true })
  @IsString()
  @IsNotEmpty()
  @Matches(/^IR\d{24}$/, {
    message: 'شماره شبا باید در فرمت ایرانی (IR و ۲۴ رقم) باشد'
  })
  iban: string;

  @IsOptional()
  @IsString()
  @Length(0, 200, { message: 'توضیحات نباید بیش از ۲۰۰ کاراکتر باشد' })
  description?: string;
}
