import { Injectable, NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { BankCard, BankCardDocument } from './schemas/bank-card.schema';
import { CreateBankCardDto } from './dto/create-bank-card.dto';
import { UpdateBankCardDto } from './dto/update-bank-card.dto';

@Injectable()
export class BankCardsService {
  constructor(
    @InjectModel(BankCard.name) private bankCardModel: Model<BankCardDocument>,
  ) {}

  /**
   * Mask card number for security (show only last 4 digits)
   */
  private maskCardNumber(cardNumber: string): string {
    return '**** **** **** ' + cardNumber.slice(-4);
  }

  /**
   * Format bank card response
   */
  private formatBankCardResponse(bankCard: BankCardDocument) {
    return {
      id: bankCard._id,
      userId: bankCard.userId,
      cardNumber: this.maskCardNumber(bankCard.cardNumber),
      bankName: bankCard.bankName,
      accountHolderName: bankCard.accountHolderName,
      iban: bankCard.iban,
      isActive: bankCard.isActive,
      description: bankCard.description,
      createdAt: (bankCard as any).createdAt,
      updatedAt: (bankCard as any).updatedAt,
    };
  }

  /**
   * Create a new bank card for a user
   */
  async create(userId: string, createBankCardDto: CreateBankCardDto): Promise<any> {
    // Check if card number already exists for this user
    const existingCard = await this.bankCardModel.findOne({
      userId: new Types.ObjectId(userId),
      cardNumber: createBankCardDto.cardNumber,
    }).exec();

    if (existingCard) {
      throw new ConflictException('این شماره کارت قبلاً برای حساب شما ثبت شده است');
    }

    // Check if IBAN already exists for this user
    const existingIban = await this.bankCardModel.findOne({
      userId: new Types.ObjectId(userId),
      iban: createBankCardDto.iban,
    }).exec();

    if (existingIban) {
      throw new ConflictException('این شماره شبا قبلاً برای حساب شما ثبت شده است');
    }

    const bankCard = new this.bankCardModel({
      ...createBankCardDto,
      userId: new Types.ObjectId(userId),
    });

    const savedCard = await bankCard.save();
    return this.formatBankCardResponse(savedCard);
  }

  /**
   * Get all bank cards for a specific user
   */
  async findAllByUser(userId: string): Promise<any[]> {
    const bankCards = await this.bankCardModel
      .find({ userId: new Types.ObjectId(userId) })
      .sort({ createdAt: -1 })
      .exec();

    return bankCards.map(card => this.formatBankCardResponse(card));
  }

  /**
   * Get a specific bank card by ID (user-specific)
   */
  async findOne(id: string, userId: string): Promise<any> {
    const bankCard = await this.bankCardModel.findById(id).exec();
    
    if (!bankCard) {
      throw new NotFoundException(`کارت بانکی با شناسه ${id} یافت نشد`);
    }

    // Check if the bank card belongs to the requesting user
    if (bankCard.userId.toString() !== userId) {
      throw new ForbiddenException('شما فقط به کارت‌های بانکی خود دسترسی دارید');
    }

    return this.formatBankCardResponse(bankCard);
  }

  /**
   * Update a bank card (user-specific)
   */
  async update(id: string, userId: string, updateBankCardDto: UpdateBankCardDto): Promise<any> {
    const bankCard = await this.bankCardModel.findById(id).exec();
    if (!bankCard) {
      throw new NotFoundException(`کارت بانکی با شناسه ${id} یافت نشد`);
    }
    if (bankCard.userId.toString() !== userId) {
      throw new ForbiddenException('شما فقط می‌توانید کارت‌های بانکی خود را ویرایش کنید');
    }
    // If updating card number, check for duplicates
    if (updateBankCardDto.cardNumber && updateBankCardDto.cardNumber !== bankCard.cardNumber) {
      const existingCard = await this.bankCardModel.findOne({
        userId: new Types.ObjectId(userId),
        cardNumber: updateBankCardDto.cardNumber,
        _id: { $ne: id },
      }).exec();
      if (existingCard) {
        throw new ConflictException('این شماره کارت قبلاً برای حساب شما ثبت شده است');
      }
    }
    // If updating IBAN, check for duplicates
    if (updateBankCardDto.iban && updateBankCardDto.iban !== bankCard.iban) {
      const existingIban = await this.bankCardModel.findOne({
        userId: new Types.ObjectId(userId),
        iban: updateBankCardDto.iban,
        _id: { $ne: id },
      }).exec();
      if (existingIban) {
        throw new ConflictException('این شماره شبا قبلاً برای حساب شما ثبت شده است');
      }
    }
    const updatedCard = await this.bankCardModel
      .findByIdAndUpdate(id, updateBankCardDto, { new: true })
      .exec();
    return this.formatBankCardResponse(updatedCard!);
  }

  /**
   * Delete a bank card (user-specific)
   */
  async remove(id: string, userId: string): Promise<void> {
    const bankCard = await this.bankCardModel.findById(id).exec();
    
    if (!bankCard) {
      throw new NotFoundException(`کارت بانکی با شناسه ${id} یافت نشد`);
    }

    // Check if the bank card belongs to the requesting user
    if (bankCard.userId.toString() !== userId) {
      throw new ForbiddenException('شما فقط می‌توانید کارت‌های بانکی خود را حذف کنید');
    }

    await this.bankCardModel.findByIdAndDelete(id).exec();
  }

  /**
   * Get active bank cards count for a user
   */
  async getActiveCardsCount(userId: string): Promise<number> {
    return this.bankCardModel.countDocuments({
      userId: new Types.ObjectId(userId),
      isActive: true,
    }).exec();
  }
}
