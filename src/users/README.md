# Users Module - <PERSON><PERSON> Authentication

This module provides OTP (One-Time Password) authentication for users using Iranian mobile numbers.

## Features

- ✅ Iranian mobile number validation (09xxxxxxxxx or +989xxxxxxxxx format)
- ✅ Automatic user registration for new mobile numbers
- ✅ 6-digit OTP generation with 5-minute expiration
- ✅ OTP verification and user authentication
- ✅ User management endpoints

## API Endpoints

### POST /users/send-otp
Sends OTP to user's mobile number. If user doesn't exist, registers them first.

**Request Body:**
```json
{
  "mobileNumber": "09123456789"
}
```

**Response (New User):**
```json
{
  "success": true,
  "message": "User registered successfully. OTP sent to your mobile number.",
  "data": {
    "mobileNumber": "09123456789",
    "expiresAt": "2025-06-08T14:00:00.000Z",
    "isNewUser": true,
    "code": "123456"
  }
}
```

**Response (Existing User):**
```json
{
  "success": true,
  "message": "OTP sent to your mobile number.",
  "data": {
    "mobileNumber": "09123456789",
    "expiresAt": "2025-06-08T14:00:00.000Z",
    "isNewUser": false,
    "code": "123456"
  }
}
```

### POST /users/verify-otp
Verifies the OTP code and authenticates the user.

**Request Body:**
```json
{
  "mobileNumber": "09123456789",
  "code": "123456"
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "OTP verified successfully",
  "data": {
    "user": {
      "id": "68459511ff030f6ef684fa17",
      "mobileNumber": "09123456789",
      "isActive": true,
      "lastLoginAt": "2025-06-08T13:51:18.381Z",
      "createdAt": "2025-06-08T13:50:09.861Z",
      "updatedAt": "2025-06-08T13:51:18.382Z"
    }
  }
}
```

**Response (Failure):**
```json
{
  "success": false,
  "message": "Invalid or expired OTP",
  "data": null
}
```

### GET /users
Returns all users (for admin purposes).

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "68459511ff030f6ef684fa17",
      "mobileNumber": "09123456789",
      "isActive": true,
      "createdAt": "2025-06-08T13:50:09.861Z",
      "updatedAt": "2025-06-08T13:51:18.382Z",
      "lastLoginAt": "2025-06-08T13:51:18.381Z"
    }
  ]
}
```

### GET /users/:id
Returns a specific user by ID.

## Mobile Number Formats

The API accepts Iranian mobile numbers in the following formats:
- `09123456789` (standard Iranian format)
- `+989123456789` (international format)

Both formats are normalized to `09xxxxxxxxx` format internally.

## Validation Rules

- Mobile number must match Iranian format regex: `/^(\+98|0)?9\d{9}$/`
- OTP code must be exactly 6 digits
- OTP expires after 5 minutes
- Only one active OTP per mobile number (previous OTPs are invalidated)

## Security Features

- OTP codes are marked as used after successful verification
- Failed verification attempts are tracked
- Expired OTPs are automatically invalidated
- Mobile number normalization prevents duplicate users

## Database Schema

### User Schema
```typescript
{
  mobileNumber: string;    // Unique, required, Iranian format
  isActive: boolean;       // Default: true
  lastLoginAt?: Date;      // Updated on successful OTP verification
  createdAt: Date;         // Auto-generated
  updatedAt: Date;         // Auto-generated
}
```

### OTP Schema
```typescript
{
  mobileNumber: string;    // Iranian format
  code: string;           // 6-digit code
  expiresAt: Date;        // 5 minutes from creation
  isUsed: boolean;        // Default: false
  attempts: number;       // Failed verification attempts
  createdAt: Date;        // Auto-generated
  updatedAt: Date;        // Auto-generated
}
```

## Testing

Run the tests with:
```bash
npm run test
```

## Example Usage

```bash
# Send OTP to new user
curl -X POST http://localhost:3001/users/send-otp \
  -H "Content-Type: application/json" \
  -d '{"mobileNumber": "09123456789"}'

# Verify OTP
curl -X POST http://localhost:3001/users/verify-otp \
  -H "Content-Type: application/json" \
  -d '{"mobileNumber": "09123456789", "code": "123456"}'
```

## Production Notes

⚠️ **Important**: In production, the OTP code should NOT be returned in the API response. Instead, it should be sent via SMS service. The current implementation returns the code for development and testing purposes only.

To integrate with SMS service:
1. Remove the `code` field from the response in `users.controller.ts`
2. Add SMS service integration in `users.service.ts`
3. Send the OTP code via SMS instead of returning it
