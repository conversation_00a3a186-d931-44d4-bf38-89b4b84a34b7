import { IsString, IsOptional, Matches, Length } from 'class-validator';

export class UpdateUserProfileDto {
  @IsOptional()
  @IsString()
  @Length(2, 50, { message: 'Name must be between 2 and 50 characters' })
  name?: string;

  @IsOptional()
  @IsString()
  @Length(2, 50, { message: 'Family name must be between 2 and 50 characters' })
  familyName?: string;

  @IsOptional()
  @IsString()
  @Matches(/^\d{10}$/, {
    message: 'National code must be exactly 10 digits'
  })
  nationalCode?: string;

  @IsOptional()
  @IsString()
  @Length(2, 50, { message: 'State must be between 2 and 50 characters' })
  state?: string;

  @IsOptional()
  @IsString()
  @Length(2, 50, { message: 'City must be between 2 and 50 characters' })
  city?: string;

  @IsOptional()
  @IsString()
  @Length(10, 200, { message: 'Address must be between 10 and 200 characters' })
  address?: string;
}
