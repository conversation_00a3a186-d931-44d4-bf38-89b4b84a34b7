import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Get,
  Param,
  Patch,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
  Request,
  BadRequestException
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { UsersService } from './users.service';
import { SendOtpDto } from './dto/send-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { UpdateUserProfileDto } from './dto/update-user-profile.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { multerConfig } from '../common/config/multer.config';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * Send OTP to user's mobile number
   * If user doesn't exist, register them first
   */
  @Post('send-otp')
  @HttpCode(HttpStatus.OK)
  async sendOtp(@Body() sendOtpDto: SendOtpDto) {
    try {
      const result = await this.usersService.sendOtp(sendOtpDto);
      
      // In production, don't return the actual code in the response
      // Instead, send it via SMS service
      return {
        success: true,
        message: result.message,
        data: {
          mobileNumber: sendOtpDto.mobileNumber,
          expiresAt: result.expiresAt,
          isNewUser: result.isNewUser,
          // Remove this line in production:
          code: process.env.NODE_ENV === 'production' ? null : result.code, // Only for development/testing
        }
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Verify OTP code and return JWT token for authentication
   */
  @Post('verify-otp')
  @HttpCode(HttpStatus.OK)
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto) {
    try {
      const result = await this.usersService.verifyOtp(verifyOtpDto);

      return {
        success: result.isValid,
        message: result.message,
        data: result.isValid ? {
          user: {
            id: result.user.id,
            mobileNumber: result.user.mobileNumber,
            isActive: result.user.isActive,
            approvalStatus: result.user.approvalStatus,
            lastLoginAt: result.user.lastLoginAt,
            createdAt: (result.user as any).createdAt,
            updatedAt: (result.user as any).updatedAt,
          },
          accessToken: result.accessToken,
          tokenType: 'Bearer'
        } : null
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Update user profile with personal data and documents
   */
  @Patch('profile')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileFieldsInterceptor([
    { name: 'nationalCardImage', maxCount: 1 },
    { name: 'authImage', maxCount: 1 },
  ], multerConfig))
  @HttpCode(HttpStatus.OK)
  async updateProfile(
    @Request() req: any,
    @Body() updateUserProfileDto: UpdateUserProfileDto,
    @UploadedFiles() files: {
      nationalCardImage?: Express.Multer.File[];
      authImage?: Express.Multer.File[];
    }
  ) {
    try {
      const userId = req.user.sub; // Get user ID from JWT payload
      const updatedUser = await this.usersService.updateUserProfile(
        userId,
        updateUserProfileDto,
        files
      );

      const isComplete = !!(
        updatedUser.name &&
        updatedUser.familyName &&
        updatedUser.nationalCode &&
        updatedUser.state &&
        updatedUser.city &&
        updatedUser.address &&
        updatedUser.nationalCardImagePath &&
        updatedUser.authImagePath
      );

      return {
        success: true,
        message: isComplete
          ? 'Profile completed and submitted for approval'
          : 'Profile updated successfully. Please complete all required fields to submit for approval.',
        data: {
          id: updatedUser.id,
          mobileNumber: updatedUser.mobileNumber,
          name: updatedUser.name,
          familyName: updatedUser.familyName,
          nationalCode: updatedUser.nationalCode,
          state: updatedUser.state,
          city: updatedUser.city,
          address: updatedUser.address,
          approvalStatus: updatedUser.approvalStatus,
          submittedAt: updatedUser.submittedAt,
          hasNationalCardImage: !!updatedUser.nationalCardImagePath,
          hasAuthImage: !!updatedUser.authImagePath,
          isProfileComplete: isComplete,
          createdAt: (updatedUser as any).createdAt,
          updatedAt: (updatedUser as any).updatedAt,
        }
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

    /**
   * Get current user's data based on the token
   */
  @UseGuards(JwtAuthGuard)
  @Get()
  async getMe(@Request() req: any) {
    try {
      const userId = req.user.sub; // Get user ID from JWT payload
      const user = await this.usersService.findOne(userId);
      return {
        success: true,
        data: user
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Get all users (for admin purposes)
   */
  @Get('/all')
  async findAll() {
    const users = await this.usersService.findAll();
    return {
      success: true,
      data: users
    };
  }

  /**
   * Get user by ID
   */
  @Get(':id')
  async findOne(@Param('id') id: string) {
    try {
      const user = await this.usersService.findOne(id);
      return {
        success: true,
        data: user
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
