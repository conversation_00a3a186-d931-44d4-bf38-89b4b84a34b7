import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { UsersService } from './users.service';
import { User, UserDocument } from './schemas/user.schema';
import { OtpService } from '../otp/otp.service';

describe('UsersService', () => {
  let service: UsersService;
  let userModel: Model<UserDocument>;
  let otpService: OtpService;

  const mockUser = {
    _id: 'mockId',
    mobileNumber: '09123456789',
    isActive: true,
    lastLoginAt: null,
    save: jest.fn().mockResolvedValue(this),
  };

  const mockUserModel = {
    findOne: jest.fn(),
    findById: jest.fn(),
    find: jest.fn(),
    constructor: jest.fn().mockResolvedValue(mockUser),
  };

  const mockOtpService = {
    createOtp: jest.fn(),
    verifyOtp: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
        {
          provide: OtpService,
          useValue: mockOtpService,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    userModel = module.get<Model<UserDocument>>(getModelToken(User.name));
    otpService = module.get<OtpService>(OtpService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendOtp', () => {
    it('should send OTP to existing user', async () => {
      const sendOtpDto = { mobileNumber: '09123456789' };
      const mockOtpResult = { code: '123456', expiresAt: new Date() };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockUser),
      });
      mockOtpService.createOtp.mockResolvedValue(mockOtpResult);

      const result = await service.sendOtp(sendOtpDto);

      expect(result.isNewUser).toBe(false);
      expect(result.code).toBe('123456');
      expect(mockOtpService.createOtp).toHaveBeenCalledWith('09123456789');
    });

    it('should register new user and send OTP', async () => {
      const sendOtpDto = { mobileNumber: '09123456789' };
      const mockOtpResult = { code: '123456', expiresAt: new Date() };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });
      mockOtpService.createOtp.mockResolvedValue(mockOtpResult);

      // Mock the constructor and save method for new user creation
      const mockNewUser = {
        ...mockUser,
        save: jest.fn().mockResolvedValue(mockUser),
      };
      (userModel as any).mockImplementation(() => mockNewUser);

      const result = await service.sendOtp(sendOtpDto);

      expect(result.isNewUser).toBe(true);
      expect(result.code).toBe('123456');
    });
  });

  describe('verifyOtp', () => {
    it('should verify OTP successfully', async () => {
      const verifyOtpDto = { mobileNumber: '09123456789', code: '123456' };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockUser),
      });
      mockOtpService.verifyOtp.mockResolvedValue(true);

      const result = await service.verifyOtp(verifyOtpDto);

      expect(result.isValid).toBe(true);
      expect(result.message).toBe('OTP verified successfully');
    });

    it('should fail OTP verification', async () => {
      const verifyOtpDto = { mobileNumber: '09123456789', code: '123456' };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockUser),
      });
      mockOtpService.verifyOtp.mockResolvedValue(false);

      const result = await service.verifyOtp(verifyOtpDto);

      expect(result.isValid).toBe(false);
      expect(result.message).toBe('Invalid or expired OTP');
    });
  });
});
