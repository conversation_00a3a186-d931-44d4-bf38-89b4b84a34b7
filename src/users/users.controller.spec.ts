import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { UpdateUserProfileDto } from './dto/update-user-profile.dto';
import { ApprovalStatus } from './schemas/user.schema';

describe('UsersController', () => {
  let controller: UsersController;
  let service: UsersService;

  const mockUser = {
    id: 'mockUserId',
    mobileNumber: '09123456789',
    name: 'احمد',
    familyName: 'محمدی',
    nationalCode: '**********',
    state: 'تهران',
    city: 'تهران',
    address: 'خیابان ولیعصر، پلاک ۱۲۳',
    approvalStatus: ApprovalStatus.PENDING,
    submittedAt: new Date(),
    nationalCardImagePath: '/path/to/national-card.jpg',
    authImagePath: '/path/to/auth-image.jpg',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockUsersService = {
    updateUserProfile: jest.fn(),
    sendOtp: jest.fn(),
    verifyOtp: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
  };

  const mockJwtAuthGuard = {
    canActivate: jest.fn(() => true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .compile();

    controller = module.get<UsersController>(UsersController);
    service = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('updateProfile', () => {
    const mockRequest = {
      user: { sub: 'mockUserId' }
    };

    const updateDto: UpdateUserProfileDto = {
      name: 'احمد',
      familyName: 'محمدی',
      nationalCode: '**********',
      state: 'تهران',
      city: 'تهران',
      address: 'خیابان ولیعصر، پلاک ۱۲۳',
    };

    const mockFiles = {
      nationalCardImage: [{ path: '/path/to/national-card.jpg' }] as Express.Multer.File[],
      authImage: [{ path: '/path/to/auth-image.jpg' }] as Express.Multer.File[],
    };

    it('should update user profile successfully', async () => {
      mockUsersService.updateUserProfile.mockResolvedValue(mockUser);

      const result = await controller.updateProfile(mockRequest, updateDto, mockFiles);

      expect(service.updateUserProfile).toHaveBeenCalledWith(
        'mockUserId',
        updateDto,
        mockFiles
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe('Profile completed and submitted for approval');
      expect(result.data.name).toBe('احمد');
      expect(result.data.state).toBe('تهران');
      expect(result.data.city).toBe('تهران');
      expect(result.data.address).toBe('خیابان ولیعصر، پلاک ۱۲۳');
      expect(result.data.approvalStatus).toBe(ApprovalStatus.PENDING);
      expect(result.data.hasNationalCardImage).toBe(true);
      expect(result.data.hasAuthImage).toBe(true);
      expect(result.data.isProfileComplete).toBe(true);
    });

    it('should handle service errors', async () => {
      const errorMessage = 'National code is already registered by another user';
      mockUsersService.updateUserProfile.mockRejectedValue(new Error(errorMessage));

      await expect(
        controller.updateProfile(mockRequest, updateDto, mockFiles)
      ).rejects.toThrow(BadRequestException);
    });

    it('should work with partial data (only text fields)', async () => {
      const partialDto = { name: 'احمد' };
      const userWithoutFiles = {
        ...mockUser,
        nationalCardImagePath: null,
        authImagePath: null,
        approvalStatus: ApprovalStatus.NOT_SUBMITTED,
        submittedAt: null
      };

      mockUsersService.updateUserProfile.mockResolvedValue(userWithoutFiles);

      const result = await controller.updateProfile(mockRequest, partialDto, {});

      expect(result.data.hasNationalCardImage).toBe(false);
      expect(result.data.hasAuthImage).toBe(false);
      expect(result.data.isProfileComplete).toBe(false);
      expect(result.message).toBe('Profile updated successfully. Please complete all required fields to submit for approval.');
    });

    it('should handle incomplete profile (missing address fields)', async () => {
      const incompleteDto = { name: 'احمد', familyName: 'محمدی' };
      const incompleteUser = {
        ...mockUser,
        state: null,
        city: null,
        address: null,
        approvalStatus: ApprovalStatus.NOT_SUBMITTED,
        submittedAt: null
      };

      mockUsersService.updateUserProfile.mockResolvedValue(incompleteUser);

      const result = await controller.updateProfile(mockRequest, incompleteDto, mockFiles);

      expect(result.data.isProfileComplete).toBe(false);
      expect(result.data.approvalStatus).toBe(ApprovalStatus.NOT_SUBMITTED);
      expect(result.message).toBe('Profile updated successfully. Please complete all required fields to submit for approval.');
    });

    it('should work with only files (no text data)', async () => {
      mockUsersService.updateUserProfile.mockResolvedValue(mockUser);

      const result = await controller.updateProfile(mockRequest, {}, mockFiles);

      expect(service.updateUserProfile).toHaveBeenCalledWith(
        'mockUserId',
        {},
        mockFiles
      );
      expect(result.success).toBe(true);
    });
  });
});
