# Orders API Testing Guide

This guide provides examples for testing the Orders API endpoints.

## Prerequisites

1. Start the application:
```bash
npm run start:dev
```

2. Get a JWT token by following the OTP authentication flow:
```bash
# Send OTP
curl -X POST http://localhost:3000/users/send-otp \
  -H "Content-Type: application/json" \
  -d '{"mobileNumber": "***********"}'

# Verify OTP and get token
curl -X POST http://localhost:3000/users/verify-otp \
  -H "Content-Type: application/json" \
  -d '{"mobileNumber": "***********", "code": "YOUR_OTP_CODE"}'
```

3. Create a bank card (required for orders):
```bash
curl -X POST http://localhost:3000/cards \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "cardNumber": "****************",
    "bankName": "Bank Melli",
    "accountHolderName": "<PERSON>",
    "iban": "**************************"
  }'
```

4. Ensure currencies exist:
```bash
# Create USD currency
curl -X POST http://localhost:3000/currencies \
  -H "Content-Type: application/json" \
  -d '{
    "name": "USD",
    "fa": "دلار آمریکا",
    "sellPrice": 50000,
    "buyPrice": 49000
  }'

# Create IRR currency
curl -X POST http://localhost:3000/currencies \
  -H "Content-Type: application/json" \
  -d '{
    "name": "IRR",
    "fa": "ریال ایران",
    "sellPrice": 1,
    "buyPrice": 1
  }'
```

## API Endpoints

### 1. Create Order (POST /orders)

```bash
curl -X POST http://localhost:3000/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "cardId": "YOUR_CARD_ID",
    "fromCurrency": "USD",
    "toCurrency": "IRR",
    "fromAmount": 100,
    "toAmount": 5000000,
    "exchangeRate": 50000,
    "depositId": "DEP123456789"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Order created successfully",
  "data": {
    "_id": "507f1f77bcf86cd799439012",
    "userId": "507f1f77bcf86cd799439011",
    "cardId": "507f1f77bcf86cd799439013",
    "fromCurrency": "USD",
    "toCurrency": "IRR",
    "fromAmount": 100,
    "toAmount": 5000000,
    "exchangeRate": 50000,
    "depositId": "DEP123456789",
    "status": "pending",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### 2. Get All Orders (GET /orders)

```bash
curl -X GET http://localhost:3000/orders \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**With status filter:**
```bash
curl -X GET "http://localhost:3000/orders?status=pending" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Orders retrieved successfully",
  "data": [
    {
      "_id": "507f1f77bcf86cd799439012",
      "userId": "507f1f77bcf86cd799439011",
      "cardId": {
        "_id": "507f1f77bcf86cd799439013",
        "bankName": "Bank Melli",
        "accountHolderName": "John Doe"
      },
      "fromCurrency": "USD",
      "toCurrency": "IRR",
      "fromAmount": 100,
      "toAmount": 5000000,
      "exchangeRate": 50000,
      "status": "pending",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "count": 1
}
```

### 3. Get Order by ID (GET /orders/:id)

```bash
curl -X GET http://localhost:3000/orders/507f1f77bcf86cd799439012 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Order retrieved successfully",
  "data": {
    "_id": "507f1f77bcf86cd799439012",
    "userId": {
      "_id": "507f1f77bcf86cd799439011",
      "mobileNumber": "***********"
    },
    "cardId": {
      "_id": "507f1f77bcf86cd799439013",
      "bankName": "Bank Melli",
      "accountHolderName": "John Doe"
    },
    "fromCurrency": "USD",
    "toCurrency": "IRR",
    "fromAmount": 100,
    "toAmount": 5000000,
    "exchangeRate": 50000,
    "status": "pending",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### 4. Update Order (PATCH /orders/:id)

```bash
curl -X PATCH http://localhost:3000/orders/507f1f77bcf86cd799439012 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "status": "processing"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Order updated successfully",
  "data": {
    "_id": "507f1f77bcf86cd799439012",
    "status": "processing",
    "updatedAt": "2023-01-01T01:00:00.000Z"
  }
}
```

### 5. Delete Order (DELETE /orders/:id)

```bash
curl -X DELETE http://localhost:3000/orders/507f1f77bcf86cd799439012 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Order deleted successfully"
}
```

## Error Scenarios

### 1. Invalid Card ID
```bash
curl -X POST http://localhost:3000/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "cardId": "invalid_card_id",
    "fromCurrency": "USD",
    "toCurrency": "IRR",
    "fromAmount": 100,
    "toAmount": 5000000,
    "exchangeRate": 50000
  }'
```

**Expected Error:**
```json
{
  "message": "Card ID must be a valid MongoDB ObjectId",
  "error": "Bad Request",
  "statusCode": 400
}
```

### 2. Card Not Owned by User
```bash
curl -X POST http://localhost:3000/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "cardId": "507f1f77bcf86cd799439999",
    "fromCurrency": "USD",
    "toCurrency": "IRR",
    "fromAmount": 100,
    "toAmount": 5000000,
    "exchangeRate": 50000
  }'
```

**Expected Error:**
```json
{
  "message": "The specified card does not belong to you or does not exist",
  "error": "Bad Request",
  "statusCode": 400
}
```

### 3. Invalid Currency
```bash
curl -X POST http://localhost:3000/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "cardId": "YOUR_CARD_ID",
    "fromCurrency": "INVALID",
    "toCurrency": "IRR",
    "fromAmount": 100,
    "toAmount": 5000000,
    "exchangeRate": 50000
  }'
```

**Expected Error:**
```json
{
  "message": "From currency 'INVALID' does not exist",
  "error": "Bad Request",
  "statusCode": 400
}
```

### 4. Exchange Rate Mismatch
```bash
curl -X POST http://localhost:3000/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "cardId": "YOUR_CARD_ID",
    "fromCurrency": "USD",
    "toCurrency": "IRR",
    "fromAmount": 100,
    "toAmount": 1000000,
    "exchangeRate": 50000
  }'
```

**Expected Error:**
```json
{
  "message": "Exchange rate calculation mismatch. Expected: 5000000.0000, Received: 1000000",
  "error": "Bad Request",
  "statusCode": 400
}
```

## Order Status Values

- `pending`: Initial status when order is created
- `processing`: Order is being processed
- `completed`: Order has been successfully completed
- `cancelled`: Order was cancelled by user or system
- `failed`: Order processing failed

## Validation Rules

- **Card ID**: Must be a valid MongoDB ObjectId and belong to the authenticated user
- **From/To Currency**: Must exist in the currencies collection
- **Amounts**: Must be positive numbers (minimum 0.01)
- **Exchange Rate**: Must be positive (minimum 0.0001)
- **Exchange Rate Calculation**: `fromAmount * exchangeRate ≈ toAmount` (1% tolerance)
- **Deposit ID**: Optional, maximum 100 characters
