# Bank Cards API Testing Guide

This guide provides examples for testing the Bank Cards CRUD API endpoints.

## Prerequisites

1. Start the application:
```bash
npm run start:dev
```

2. Get a JWT token by following the OTP authentication flow:
```bash
# Send OTP
curl -X POST http://localhost:3000/users/send-otp \
  -H "Content-Type: application/json" \
  -d '{"mobileNumber": "***********"}'

# Verify OTP and get token
curl -X POST http://localhost:3000/users/verify-otp \
  -H "Content-Type: application/json" \
  -d '{"mobileNumber": "***********", "code": "YOUR_OTP_CODE"}'
```

Save the `accessToken` from the response for use in the following requests.

## API Testing Examples

### 1. Create a Bank Card

```bash
curl -X POST http://localhost:3000/bank-cards \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "cardNumber": "****************",
    "bankName": "بانک ملت",
    "accountHolderName": "احمد محمدی",
    "iban": "**************************",
    "description": "کارت اصلی"
  }'
```

### 2. Get All Bank Cards

```bash
curl -X GET http://localhost:3000/bank-cards \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### 3. Get Active Cards Count

```bash
curl -X GET http://localhost:3000/bank-cards/active-count \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### 4. Get a Specific Bank Card

```bash
curl -X GET http://localhost:3000/bank-cards/BANK_CARD_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### 5. Update a Bank Card

```bash
curl -X PATCH http://localhost:3000/bank-cards/BANK_CARD_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "bankName": "بانک صادرات",
    "description": "کارت به‌روزرسانی شده"
  }'
```

### 6. Deactivate a Bank Card

```bash
curl -X PATCH http://localhost:3000/bank-cards/BANK_CARD_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "isActive": false
  }'
```

### 7. Delete a Bank Card

```bash
curl -X DELETE http://localhost:3000/bank-cards/BANK_CARD_ID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

## Expected Response Formats

### Success Response (Create/Update/Get)
```json
{
  "success": true,
  "message": "Bank card created successfully",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "userId": "507f1f77bcf86cd799439012",
    "cardNumber": "**** **** **** 3456",
    "bankName": "بانک ملت",
    "accountHolderName": "احمد محمدی",
    "iban": "**************************",
    "isActive": true,
    "description": "کارت اصلی",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Success Response (Get All)
```json
{
  "success": true,
  "message": "Bank cards retrieved successfully",
  "data": [
    {
      "id": "507f1f77bcf86cd799439011",
      "userId": "507f1f77bcf86cd799439012",
      "cardNumber": "**** **** **** 3456",
      "bankName": "بانک ملت",
      "accountHolderName": "احمد محمدی",
      "iban": "**************************",
      "isActive": true,
      "description": "کارت اصلی",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "count": 1
}
```

### Error Response Examples

#### Validation Error (400)
```json
{
  "statusCode": 400,
  "message": [
    "Card number must be exactly 16 digits",
    "IBAN must be in Iranian format (IR followed by 24 digits)"
  ],
  "error": "Bad Request"
}
```

#### Unauthorized (401)
```json
{
  "statusCode": 401,
  "message": "Unauthorized"
}
```

#### Forbidden (403)
```json
{
  "statusCode": 403,
  "message": "You can only access your own bank cards"
}
```

#### Not Found (404)
```json
{
  "statusCode": 404,
  "message": "Bank card with ID 507f1f77bcf86cd799439011 not found"
}
```

#### Conflict (409)
```json
{
  "statusCode": 409,
  "message": "This card number is already registered for your account"
}
```

## Validation Rules

- **Card Number**: Must be exactly 16 digits
- **Bank Name**: 2-50 characters
- **Account Holder Name**: 2-100 characters  
- **IBAN**: Must match Iranian format `IR` + 24 digits
- **Description**: Optional, max 200 characters
- **Duplicate Prevention**: Card number and IBAN must be unique per user

## Security Features

- Card numbers are automatically masked in responses (only last 4 digits shown)
- Users can only access their own bank cards
- JWT authentication required for all endpoints
- Comprehensive validation and error handling
