# JWT Authentication Test Guide

## Overview
The `verify-otp` endpoint now returns a JWT token for authentication in future API calls.

## API Flow

### 1. Send OTP
```bash
curl -X POST http://localhost:3001/users/send-otp \
  -H "Content-Type: application/json" \
  -d '{
    "mobileNumber": "09123456789"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully. OTP sent to your mobile number.",
  "data": {
    "mobileNumber": "09123456789",
    "expiresAt": "2025-06-08T17:41:39.000Z",
    "isNewUser": true,
    "code": "123456"
  }
}
```

### 2. Verify OTP (Now returns JWT token)
```bash
curl -X POST http://localhost:3001/users/verify-otp \
  -H "Content-Type: application/json" \
  -d '{
    "mobileNumber": "09123456789",
    "code": "123456"
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "<PERSON><PERSON> verified successfully",
  "data": {
    "user": {
      "id": "676b1234567890abcdef1234",
      "mobileNumber": "09123456789",
      "isActive": true,
      "lastLoginAt": "2025-06-08T17:36:40.000Z",
      "createdAt": "2025-06-08T17:36:39.000Z",
      "updatedAt": "2025-06-08T17:36:40.000Z"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer"
  }
}
```

### 3. Use JWT Token in Future API Calls
```bash
curl -X GET http://localhost:3001/users \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## JWT Token Details

- **Algorithm**: HS256
- **Expiration**: 7 days (configurable via JWT_EXPIRES_IN)
- **Payload includes**:
  - `sub`: User ID
  - `mobileNumber`: User's mobile number
  - `iat`: Issued at timestamp
  - `exp`: Expiration timestamp

## Environment Variables

Add these to your `.env` file:
```
JWT_SECRET=your-super-secret-jwt-key-change-in-production-please
JWT_EXPIRES_IN=7d
```

## Security Notes

1. Change the JWT_SECRET in production
2. Use HTTPS in production
3. Store tokens securely on the client side
4. Implement token refresh mechanism for long-lived applications
