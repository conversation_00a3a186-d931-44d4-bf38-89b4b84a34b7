import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Users OTP API (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/users/send-otp (POST)', () => {
    it('should send OTP for valid Iranian mobile number', () => {
      return request(app.getHttpServer())
        .post('/users/send-otp')
        .send({ mobileNumber: '09123456789' })
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.mobileNumber).toBe('09123456789');
          expect(res.body.data.code).toMatch(/^\d{6}$/);
          expect(res.body.data.expiresAt).toBeDefined();
          expect(typeof res.body.data.isNewUser).toBe('boolean');
        });
    });

    it('should accept +98 format mobile number', () => {
      return request(app.getHttpServer())
        .post('/users/send-otp')
        .send({ mobileNumber: '+989123456789' })
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.mobileNumber).toBe('+989123456789');
        });
    });

    it('should reject invalid mobile number format', () => {
      return request(app.getHttpServer())
        .post('/users/send-otp')
        .send({ mobileNumber: '123456789' })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('Mobile number must be in Iranian format');
        });
    });

    it('should reject empty mobile number', () => {
      return request(app.getHttpServer())
        .post('/users/send-otp')
        .send({ mobileNumber: '' })
        .expect(400);
    });
  });

  describe('/users/verify-otp (POST)', () => {
    let otpCode: string;
    const testMobile = '09987654321';

    beforeEach(async () => {
      // First, send OTP to get the code
      const response = await request(app.getHttpServer())
        .post('/users/send-otp')
        .send({ mobileNumber: testMobile })
        .expect(200);
      
      otpCode = response.body.data.code;
    });

    it('should verify valid OTP', () => {
      return request(app.getHttpServer())
        .post('/users/verify-otp')
        .send({ 
          mobileNumber: testMobile,
          code: otpCode 
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.message).toBe('OTP verified successfully');
          expect(res.body.data.user.mobileNumber).toBe(testMobile);
          expect(res.body.data.user.id).toBeDefined();
          expect(res.body.data.user.lastLoginAt).toBeDefined();
        });
    });

    it('should reject invalid OTP code', () => {
      return request(app.getHttpServer())
        .post('/users/verify-otp')
        .send({ 
          mobileNumber: testMobile,
          code: '000000' 
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(false);
          expect(res.body.message).toBe('Invalid or expired OTP');
          expect(res.body.data).toBe(null);
        });
    });

    it('should reject invalid mobile number format', () => {
      return request(app.getHttpServer())
        .post('/users/verify-otp')
        .send({ 
          mobileNumber: '123456789',
          code: otpCode 
        })
        .expect(400);
    });

    it('should reject invalid OTP format', () => {
      return request(app.getHttpServer())
        .post('/users/verify-otp')
        .send({ 
          mobileNumber: testMobile,
          code: '12345' // Only 5 digits
        })
        .expect(400);
    });
  });

  describe('/users (GET)', () => {
    it('should return list of users', () => {
      return request(app.getHttpServer())
        .get('/users')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(Array.isArray(res.body.data)).toBe(true);
        });
    });
  });

  describe('Complete OTP Flow', () => {
    it('should complete full registration and verification flow', async () => {
      const testMobile = '09111222333';

      // Step 1: Send OTP for new user
      const sendOtpResponse = await request(app.getHttpServer())
        .post('/users/send-otp')
        .send({ mobileNumber: testMobile })
        .expect(200);

      expect(sendOtpResponse.body.data.isNewUser).toBe(true);
      expect(sendOtpResponse.body.message).toContain('User registered successfully');
      
      const otpCode = sendOtpResponse.body.data.code;

      // Step 2: Verify OTP
      const verifyResponse = await request(app.getHttpServer())
        .post('/users/verify-otp')
        .send({ 
          mobileNumber: testMobile,
          code: otpCode 
        })
        .expect(200);

      expect(verifyResponse.body.success).toBe(true);
      expect(verifyResponse.body.data.user.mobileNumber).toBe(testMobile);
      
      const userId = verifyResponse.body.data.user.id;

      // Step 3: Send OTP again (existing user)
      const sendOtpResponse2 = await request(app.getHttpServer())
        .post('/users/send-otp')
        .send({ mobileNumber: testMobile })
        .expect(200);

      expect(sendOtpResponse2.body.data.isNewUser).toBe(false);
      expect(sendOtpResponse2.body.message).toBe('OTP sent to your mobile number.');

      // Step 4: Get user by ID
      await request(app.getHttpServer())
        .get(`/users/${userId}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.mobileNumber).toBe(testMobile);
        });
    });
  });
});
