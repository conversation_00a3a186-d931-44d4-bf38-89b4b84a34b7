# User Profile Update API Test Guide

## Overview
The `PATCH /users/profile` endpoint allows authenticated users to submit their personal data and documents for approval.

## Prerequisites
1. User must be authenticated (have a valid JWT token from the `verify-otp` endpoint)
2. Files must be image formats (jpg, jpeg, png, gif)
3. File size limit: 5MB per file

## API Endpoint

### PATCH /users/profile
Updates user profile with personal data and document uploads.

**Authentication Required**: Yes (Bearer token)

**Content-Type**: `multipart/form-data`

**Request Fields**:
- `name` (optional): User's first name (2-50 characters)
- `familyName` (optional): User's family name (2-50 characters)
- `nationalCode` (optional): Iranian national code (exactly 10 digits)
- `state` (optional): User's state/province (2-50 characters)
- `city` (optional): User's city (2-50 characters)
- `address` (optional): User's full address (10-200 characters)
- `nationalCardImage` (optional): National card image file
- `authImage` (optional): Authentication image file

**Profile Completion Requirements**:
For the profile to be submitted for approval (status = "pending"), ALL of the following fields must be provided:
- name, familyName, nationalCode, state, city, address, nationalCardImage, authImage

If any required field is missing, the profile will remain in "not_submitted" status until all fields are completed.

## Example Usage

### 1. First, get a JWT token (if you don't have one)

```bash
# Send OTP
curl -X POST http://localhost:3000/users/send-otp \
  -H "Content-Type: application/json" \
  -d '{
    "mobileNumber": "09123456789"
  }'

# Verify OTP and get token
curl -X POST http://localhost:3000/users/verify-otp \
  -H "Content-Type: application/json" \
  -d '{
    "mobileNumber": "09123456789",
    "code": "123456"
  }'
```

### 2. Update profile with complete data and documents (will be submitted for approval)

```bash
curl -X PATCH http://localhost:3000/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -F "name=احمد" \
  -F "familyName=محمدی" \
  -F "nationalCode=1234567890" \
  -F "state=تهران" \
  -F "city=تهران" \
  -F "address=خیابان ولیعصر، پلاک ۱۲۳، واحد ۴" \
  -F "nationalCardImage=@/path/to/national-card.jpg" \
  -F "authImage=@/path/to/selfie.jpg"
```

### 3. Example with partial data (will NOT be submitted for approval)

```bash
curl -X PATCH http://localhost:3000/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -F "name=احمد" \
  -F "familyName=محمدی" \
  -F "nationalCode=1234567890"
```

### 4. Example with address fields only

```bash
curl -X PATCH http://localhost:3000/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -F "state=اصفهان" \
  -F "city=اصفهان" \
  -F "address=خیابان چهارباغ، کوچه گلستان، پلاک ۵۶"
```

### 5. Example with only files (no text data)

```bash
curl -X PATCH http://localhost:3000/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -F "nationalCardImage=@/path/to/national-card.jpg" \
  -F "authImage=@/path/to/selfie.jpg"
```

## Response Format

### Success Response - Complete Profile (200 OK)
```json
{
  "success": true,
  "message": "Profile completed and submitted for approval",
  "data": {
    "id": "user_id_here",
    "mobileNumber": "09123456789",
    "name": "احمد",
    "familyName": "محمدی",
    "nationalCode": "1234567890",
    "state": "تهران",
    "city": "تهران",
    "address": "خیابان ولیعصر، پلاک ۱۲۳، واحد ۴",
    "approvalStatus": "pending",
    "submittedAt": "2025-01-08T10:30:00.000Z",
    "hasNationalCardImage": true,
    "hasAuthImage": true,
    "isProfileComplete": true,
    "createdAt": "2025-01-08T09:00:00.000Z",
    "updatedAt": "2025-01-08T10:30:00.000Z"
  }
}
```

### Success Response - Incomplete Profile (200 OK)
```json
{
  "success": true,
  "message": "Profile updated successfully. Please complete all required fields to submit for approval.",
  "data": {
    "id": "user_id_here",
    "mobileNumber": "09123456789",
    "name": "احمد",
    "familyName": "محمدی",
    "nationalCode": "1234567890",
    "state": null,
    "city": null,
    "address": null,
    "approvalStatus": "not_submitted",
    "submittedAt": null,
    "hasNationalCardImage": false,
    "hasAuthImage": false,
    "isProfileComplete": false,
    "createdAt": "2025-01-08T09:00:00.000Z",
    "updatedAt": "2025-01-08T10:30:00.000Z"
  }
}
```

### Error Responses

#### 401 Unauthorized (Missing or invalid token)
```json
{
  "message": "Access token is required",
  "error": "Unauthorized",
  "statusCode": 401
}
```

#### 400 Bad Request (Validation errors)
```json
{
  "message": "National code must be exactly 10 digits",
  "error": "Bad Request", 
  "statusCode": 400
}
```

#### 409 Conflict (National code already exists)
```json
{
  "message": "National code is already registered by another user",
  "error": "Bad Request",
  "statusCode": 400
}
```

## File Storage
- Uploaded files are stored in `uploads/user-documents/` directory
- File naming format: `{userId}_{fieldname}_{timestamp}.{extension}`
- Example: `507f1f77bcf86cd799439011_nationalCardImage_1704708600000.jpg`

## Validation Rules
- **name**: 2-50 characters, optional
- **familyName**: 2-50 characters, optional
- **nationalCode**: Exactly 10 digits, must be unique across all users, optional
- **state**: 2-50 characters, optional
- **city**: 2-50 characters, optional
- **address**: 10-200 characters, optional
- **Files**: Must be images (jpg, jpeg, png, gif), max 5MB each, optional

## Security Features
- JWT authentication required
- File type validation (images only)
- File size limits (5MB)
- National code uniqueness validation
- Automatic approval status reset to "pending" on each update

## Approval Status Logic
- **not_submitted**: Default status when profile is incomplete
- **pending**: Set automatically when ALL required fields are provided (name, familyName, nationalCode, state, city, address, nationalCardImage, authImage)
- **approved**: Set by administrators after review
- **rejected**: Set by administrators if documents/information are invalid

The `submittedAt` timestamp is only set when the profile becomes complete and status changes to "pending".

## Notes
- All fields are optional - you can update just name, just files, or any combination
- Status only changes to "pending" when ALL required fields are complete
- Partial updates keep the current status (usually "not_submitted")
- Files are stored securely on the server with unique names
- National code must be unique across all users in the system
- The API response includes `isProfileComplete` flag to help frontend show completion status
